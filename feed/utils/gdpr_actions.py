"""
GDPR Actions Utility

This module provides utility functions for handling GDPR-related actions
such as user consent management and data anonymization.

"""

from django.http import JsonResponse
from feed.models import *

def delete_application_data(application_id: str):
    """
    Delete personal data of a specific application.

    Args:
        application_id (str): The unique identifier of the application.
    """
    print(f"Deleting application data for application_id: {application_id}")

    try:
        application = Application.objects.get(application_id=application_id)
        candidate_id = application.candidate_id
        candidate = Candidate.objects.get(candidate_id=candidate_id.candidate_id)
        candidate.delete()
        application.delete()
    except Application.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Application not found.'})
    except Candidate.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Candidate not found.'})


    return JsonResponse({'success': True, 'message': 'Application and candidate data deleted successfully.'})