{% extends 'main.html' %}
{% block content %}
{% load static %}
{% load initials_avatar %}
{% load i18n %}

<style>
  /* Toast Notification Styles */
  .toast {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    background-color: white;
    border-left: 4px solid #10b981; /* Success green */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    pointer-events: none;
    border-radius: 4px;
    overflow: hidden;
  }

  .toast.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  .toast.error {
    border-left-color: #ef4444; /* Error red */
  }

  .toast-header {
    padding: 0.75rem 1rem;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }

  .toast-body {
    padding: 1rem;
  }

  .toast-close {
    cursor: pointer;
    background: none;
    border: none;
    font-size: 1.25rem;
  }
</style>
  <style>
    /* Live Email Preview (matches emails/base_email.html roughly) */
    .email-preview { background-color: #f8f9fa; padding: 12px; border-radius: 6px; }
    .email-preview .email-container { max-width: 600px; margin: 0; background-color: #fff; box-shadow: 0 2px 10px rgba(0,0,0,0.06); border-radius: 6px; overflow: hidden; }
    .email-preview .email-header { background: linear-gradient(135deg, #131313 0%, #0f0f0f 100%); padding: 20px 28px; text-align: center; color: #777; }
    .email-preview .company-logo { font-size: 22px; font-weight: 700; margin-bottom: 6px; }
    .email-preview .email-subtitle { font-size: 14px; }
    .email-preview .email-body { padding: 28px; color: #333; }
    .email-preview .greeting { font-size: 16px; margin-bottom: 14px; color: #2c3e50; }
    .email-preview .content-section p { margin-bottom: 10px; font-size: 15px; line-height: 1.6; }
    .email-preview .signature { margin-top: 20px; padding-top: 12px; border-top: 1px solid #e9ecef; }
    .email-preview .signature-name { font-weight: 600; color: #2c3e50; }
    .email-preview .signature-title { color: #6c757d; font-size: 13px; }
    .email-preview .email-footer { background-color: #f8f9fa; padding: 16px 24px; text-align: center; border-top: 1px solid #e9ecef; }
    .email-preview .footer-text { color: #6c757d; font-size: 13px; }
    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .highlight-box {
        background-color: #f8f9fa;
        border-left: 4px solid #0f0f0f;
        padding: 20px;
        margin: 25px 0;
        border-radius: 0 8px 8px 0;
    }
    
    .info-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .info-table th,
    .info-table td {
        padding: 15px 20px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        width: 35%;
    }
    
    .info-table td {
        color: #6c757d;
    }
    
    .info-table tr:last-child th,
    .info-table tr:last-child td {
        border-bottom: none;
    }
        
    .status-new { background-color: #e3f2fd; color: #1976d2; }
    .status-review { background-color: #fff3e0; color: #f57c00; }
    .status-hired { background-color: #e8f5e8; color: #388e3c; }
    .status-rejected { background-color: #ffebee; color: #d32f2f; }
  </style>


<div class="container mx-auto px-4 py-8">
  <!-- Toast Notification -->
  <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header d-flex justify-content-between align-items-center">
      <strong class="toast-title">{% trans "Notification" %}</strong>
      <button type="button" class="btn-close toast-close" aria-label="{ % trans 'Close' %}"></button>
    </div>
    <div class="toast-body">
      <span class="toast-message"></span>
    </div>
  </div>
  <!-- Position Header -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="row">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="text-2xl font-bold text-gray-800 mb-0">
            {{ vacancy.vacancy_title }}
          </h1>
          <div class="d-flex gap-3">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-50">
              {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-50">
              {{ vacancy.vacancy_status }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-800">
              {% trans "Total Applicants:" %}
              <span class="ml-2 font-bold">{{ application_count }}</span>
            </span>
          </div>
          <h4 class="text-base text-gray-500 mb-0">
            {% trans "Published At:" %}
            <span data-toggle="tooltip" title="{{ vacancy.days_open }} Days Ago">
              {{ vacancy.vacancy_creation_date|date:"F j, Y" }}
            </span>
          </h4>
        </div>
      </div>
    </div>

    <hr>

    <!-- Control Buttons Section -->
    <div class="row">
      <div class="col-12">
        <div class="d-flex flex-wrap gap-3">
          <button type="button" id="commBtn" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#communicationModal">
            <i class="bi bi-chat-left-text mx-2"></i>{% trans "Bulk Communication" %}
          </button>
          <button type="button" id="viewJobDescButton" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#jobDescriptionModal">
            <i class="bi bi-file-text mx-2"></i>{% trans "View Job Description" %}
          </button>
          <button type="button" id="postLinkedInButton" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#postLinkedInModal">
            <i class="bi bi-linkedin mx-2"></i>{% trans "Post on LinkedIn" %}
          </button>
          <button type="button" id="postAnyPortalButton" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#postAnyPortalModal">
            <i class="bi bi-globe mx-2"></i>{% trans "Post on Any Portal" %}
          </button>
          <button type="button" id="chngStatusBtn" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
            <i class="bi bi-gear mx-2"></i>{% trans "Change Vacancy Status" %}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="grid grid-cols-1 gap-6 mb-6">
    <!-- Applicants Over Time Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">
          {% trans "Applicants Over Time" %}
        </h2>
      </div>
      {% if application_count == 0 %}
        <div class="text-center py-12">
          <div class="mb-4">
            <i class="fas fa-chart-line text-gray-300 text-6xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-600 mb-2">{% trans "No Application Data Yet" %}</h3>
          <p class="text-gray-500 text-sm">{% trans "Once candidates start applying, you'll see application trends over time here." %}</p>
        </div>
      {% else %}
        <canvas id="applicantsLineChart" height="60"></canvas>
      {% endif %}
    </div>
  </div>

  <!-- Secondary Charts Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Job Portal Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">
        {% trans "Number of Applicants by Job Portal" %}
      </h2>
      {% if application_count == 0 %}
        <div class="text-center py-12">
          <div class="mb-4">
            <i class="fas fa-chart-bar text-gray-300 text-6xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-600 mb-2">{% trans "No Portal Data Yet" %}</h3>
          <p class="text-gray-500 text-sm">{% trans "When applications come in, you'll see which job portals are most effective here." %}</p>
        </div>
      {% else %}
        <canvas id="jobPortalBarChart" height="200"></canvas>
      {% endif %}
    </div>

    <!-- Applicant Status Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">
        {% trans "Distribution of Applicants by Status" %}
      </h2>
      {% if application_count == 0 %}
        <div class="text-center py-12">
          <div class="mb-4">
            <i class="fas fa-chart-pie text-gray-300 text-6xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-600 mb-2">{% trans "No Status Data Yet" %}</h3>
          <p class="text-gray-500 text-sm">{% trans "Application status distribution will appear here as you review candidates." %}</p>
        </div>
      {% else %}
        <div class="flex justify-center">
          <div style="max-width: 450px; max-height: 450px">
            <canvas id="statusPieChart"></canvas>
          </div>
        </div>
      {% endif %}
    </div>
  </div>

  <!-- Top Applicants Table -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-800">{% trans "Top Applicants" %}</h2>
      {% if application_count > 0 %}
        {% if user_permissions.can_manage_candidates or is_admin %}
        <a
          href="/people/?position={{ all_applicants_url }}&page=1"
          class="btn btn-purple text-white "
          style="background-color: #6b46c1; border-color: #6b46c1; hover:bg-purple-700;"
        >
          <i class="bi bi-people-fill mx-1"></i> {% trans "View All Applicants" %}
        </a>
        {% endif %}
      {% endif %}
    </div>

    {% if application_count == 0 %}
    <!-- No Applicants Message -->
    <div class="text-center py-12">
      <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
        <i class="bi bi-people text-blue-600 text-2xl"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        {% trans "No Applicants Yet" %}
      </h3>
      <p class="text-gray-500 mb-4 max-w-md mx-auto">
        {% trans "Don't worry! Once candidates start applying to this position, they will appear here. You can track their progress, review their profiles, and manage the hiring process." %}
      </p>
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-lg mx-auto">
        <div class="flex items-start">
          <i class="bi bi-info-circle text-blue-600 mt-0.5 mr-3"></i>
          <div class="text-left">
            <p class="text-sm text-blue-800 font-medium mb-1">
              {% trans "What happens next?" %}
            </p>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• {% trans "Candidates will apply through your job posting" %}</li>
              <li>• {% trans "You'll see their profiles and CVs here" %}</li>
              <li>• {% trans "You can review, rate, and manage applications" %}</li>
              <li>• {% trans "Use the communication tools to contact candidates" %}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    {% else %}
    <!-- Applicants Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-x divide-gray-200 border">
        <!-- Table Headers -->
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Name" %}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Status" %}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Score" %}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Actions" %}
            </th>
          </tr>
        </thead>
        <!-- Table Body -->
        <tbody class="bg-white divide-y divide-x divide-gray-200">
          {% for applicant in top_applicants %}
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div
                  style="background-color: {{ applicant.candidate_id.avatar_bg_color }};
                         width: 40px; height: 40px; border-radius: 50%;
                         display: flex; align-items: center; justify-content: center;
                         color: white; font-weight: bold;"
                >
                  {{ applicant.candidate_id.candidate_firstname|slice:":1" }}{{ applicant.candidate_id.candidate_lastname|slice:":1" }}
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    {{ applicant.candidate_id.candidate_firstname }} {{ applicant.candidate_id.candidate_lastname }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ applicant.candidate_id.candidate_email }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                {{ applicant.application_state }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <div class="flex items-center">
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div class="h-2.5 rounded-full applicant-score-bar"
                       style="width: {{ applicant.score }}%; background-color: #3b82f6;"
                       data-score="{{ applicant.score }}">
                  </div>
                </div>
                {% if applicant.score != -1 %}
                <span class="ml-2 text-sm font-medium text-gray-900">
                  {{ applicant.score }}%
                </span>
                {% else %}
                <span class="ml-2 text-sm font-medium text-gray-900">
                  {% trans "Not Rated" %}
                </span>
                {% endif %}

              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
              <a
                href="{% url 'application' application_id=applicant.application_id %}"
                class="text-indigo-600 hover:text-indigo-900 mr-2"
              >
                {% trans "View" %}
              </a>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% endif %}
  </div>
</div>

<!-- Talent Request Modal -->
<div class="modal fade" id="talentRequestModal" tabindex="-1" aria-labelledby="talentRequestModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="talentRequestModalLabel">{% trans "Request Support From Experts" %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="text-gray-700">
          {% trans "We can provide vetted candidates from our talent pool, or help you during the technical interviews to pick best fit for your expectations." %}
        </p>
        <form id="talentRequestForm" method="POST" action="{% url 'insert_talent_request' %}">
          {% csrf_token %}
          <div class="mb-3">
            <label for="talentRequestField" class="form-label">{% trans "Enter Details" %}</label>
            <textarea
              class="form-control"
              id="talentRequestField"
              name="request_notes"
              maxlength="500"
              placeholder="Enter your additional request notes (optional)"
            ></textarea>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
            <button type="submit" class="btn btn-primary" name="request_type" value="candidate_req">{% trans "Request Candidates" %}</button>
            <button type="submit" class="btn btn-primary" name="request_type" value="interview_expert_req">{% trans "Request Interview Help" %}</button>
          </div>
          <input type="hidden" name="vacancy_id" value="{{ vacancy.vacancy_id }}" />
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Communication Modal -->
<div class="modal fade" id="communicationModal" tabindex="-1" aria-labelledby="communicationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="communicationModalLabel">{% trans "Send Bulk Mail to Applicants" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-gray-700">
                    {% trans "Use this form to send bulk emails to applicants based on their application statuses." %}
                </p>
                <form id="bulkMailForm" method="POST" action="{% url 'send_bulk_mails' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="applicationStatus" class="form-label">{% trans "Select Application Status" %}</label>
                        <select
                            class="form-select"
                            id="applicationStatus"
                            name="application_status"
                            required
                        >
                            <option value="" disabled selected>{% trans "Select a status" %}</option>
                            {% for state in applications_by_state %}
                            <option value="{{ state.application_state }}">{{ state.application_state }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="emailSubject" class="form-label">{% trans "Email Subject" %}</label>
                        <input
                            type="text"
                            class="form-control"
                            id="emailSubject"
                            name="email_subject"
                            maxlength="100"
                            placeholder="Enter email subject"
                            required
                        />
                    </div>
                    <div class="mb-3">
                        <label for="internalNotes" class="form-label">{% trans "Internal Notes" %} <small class="text-muted">{% trans "(visible only to recruiters)" %}</small></label>
                        <textarea
                            class="form-control"
                            id="internalNotes"
                            name="internal_notes"
                            rows="3"
                            maxlength="500"
                            placeholder="{% trans 'Enter internal notes for your team (optional)' %}"
                        ></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="emailBody" class="form-label">{% trans "Email Body" %} <small class="text-muted">{% trans "(sent to candidates)" %}</small></label>
                        <textarea
                            class="form-control"
                            id="emailBody"
                            name="email_body"
                            rows="5"
                            maxlength="1000"
                            placeholder="{% trans 'Enter your email message' %}"
                            required
                        ></textarea>
                    </div>
                        <!-- Live Email Preview -->
                        <div class="mt-4 mx-auto">
                            <h6 class="mb-2">{% trans "Email Preview" %}</h6>
                            <div class="small text-muted mb-2">
                                {% trans "Subject:" %} <span id="previewSubject"></span>
                            </div>
                            <div class="email-preview" id="emailPreviewContainer">
                                <div class="email-container">
                                    <div class="email-header">
                                        <div class="company-logo">{{ request.employer.employer_name }}</div>
                                        <div class="email-subtitle">{% trans "Message from Recruitment Team" %}</div>
                                    </div>
                                    <div class="email-body">
                                        <div class="greeting">
                                            {% trans "Dear" %} {% trans "{Candidate's Name}" %},
                                        </div>

                                        <div class="content-section">
                                            <p>
                                                {% blocktrans with job_title=vacancy.vacancy_title %}
                                                This message is regarding your application for the position of <strong>{{ job_title }}</strong>.
                                                {% endblocktrans %}
                                            </p>
                                        </div>

                                        <div class="content-section mt-2" id="previewEmailBody"></div>

                                    </div>
                                    <div class="highlight-box">
                                        <table class="info-table">
                                            <tr>
                                                <th>{% trans "Position" %}</th>
                                                <td>{{ vacancy.vacancy_title }}</td>
                                            </tr>
                                            <tr>
                                                <th>{% trans "Application Date" %}</th>
                                                <td> {% trans "{Application Date}" %} </td>
                                            </tr>
                                            <tr>
                                                <th>{% trans "Current Status" %}</th>
                                                <td>
                                                    <span class="status-badge status-new">
                                                        {% trans "{Application Status}" %}
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    
                                    <div class="email-body">
                                      <div class="content-section">

                                        <p class="text-muted mt-4">
                                            {% trans "Thank you for your continued interest in our company. We appreciate your patience throughout the application process." %}
                                        </p>

                                        <div class="signature">
                                            <div class="signature-name">{{ request.employer.employer_name }} {% trans "Recruitment Team" %}</div>
                                            <div class="signature-title">{% trans "Human Resources Department" %}</div>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    <div class="email-footer mt-4">
                                      <div class="footer-text">
                                          {% trans "If you have any questions, please don't hesitate to contact us." %}
                                      </div>
                                      <div class="footer-text">
                                          {% trans "Reference ID:" %} <strong> {% trans "{Reference ID}" %} </strong>
                                      </div>
                                      <div class="footer-text">
                                          {% trans "This email was sent by" %} {{ request.employer.employer_name }}
                                      </div>
                                  </div>
                                </div>
                            </div>
                        </div>

                    <div class="mb-3 form-check">
                        <input
                            type="checkbox"
                            class="form-check-input"
                            id="notifyCandidates"
                            name="notify_candidates"
                            checked
                        />
                        <label class="form-check-label" for="notifyCandidates">{% trans "Send notification emails to candidates" %}</label>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" id="sendBulkEmailsBtn" class="btn btn-primary">{% trans "Send Emails" %}</button>
                    </div>
                    <input type="hidden" name="vacancy_id" value="{{ vacancy.vacancy_id }}" />
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Post on LinkedIn Modal -->
<div class="modal fade" id="postLinkedInModal" tabindex="-1" aria-labelledby="postLinkedInModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white ">
                <h5 class="modal-title d-flex align-items-center" id="postLinkedInModalLabel">
                    <i class="bi bi-linkedin me-2"></i>
                    {% trans "Post this job on LinkedIn" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="alert alert-info mb-4">
                    <i class="bi bi-info-circle me-2"></i>
                    {% trans "Follow these simple steps to post your job on LinkedIn and reach more candidates." %}
                </div>

                <!-- Step 1 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">1</span>
                        <h6 class="mb-0 mx-1">{% trans "Navigate to LinkedIn Job Posting" %}</h6>
                    </div>
                    <p class="step-description ms-5 mb-3">
                        {% trans "Go to" %} <a href="https://www.linkedin.com/job-posting/v2/" target="_blank" class="text-decoration-none fw-bold">{% trans "LinkedIn Job Posting Page" %}</a>
                    </p>
                </div>

                <!-- Step 2 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">2</span>
                        <h6 class="mb-0 mx-1">{% trans "Copy Job Title" %}</h6>
                    </div>
                    <p class="step-description ms-5 mb-3">{% trans "Copy and paste the job title below:" %}</p>

                    <div class="copy-section ms-5">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light" value="{{ vacancy.vacancy_title }}" readonly>
                            <button class="btn btn-outline-primary d-flex align-items-center" onclick="copyToClipboard('{{ vacancy.vacancy_title }}', event)">
                                <i class="bi bi-clipboard me-1"></i>
                                {% trans "Copy" %}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">3</span>
                        <h6 class="mb-0 mx-1">{% trans "Configure Job Settings" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <p class="mb-2">{% trans "Select 'Use my own description' option and configure job details:" %}</p>
                        <div class="alert alert-light border">
                            <small class="text-muted">{% trans "Reference - Your job settings:" %}</small>
                            <div class="mt-2">
                                <p class="mb-1"><strong>{% trans "Location:" %}</strong> {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}</p>
                                <p class="mb-1"><strong>{% trans "Work Schedule:" %}</strong> {{ vacancy.work_schedule }}</p>
                                <p class="mb-0"><strong>{% trans "Office Schedule:" %}</strong> {{ vacancy.office_schedule }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">4</span>
                        <h6 class="mb-0 mx-1">{% trans "Copy Job Description" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <p class="mb-3">{% trans "Copy and paste the job description below:" %}</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <small>{% trans "Note: Delete any placeholder text in LinkedIn's editor before pasting. You may have to re-apply some of the styling." %}</small>
                        </div>

                        <div class="copy-section">
                            <div class="input-group">
                                <div class="form-control bg-light job-description-copy-area" style="height: 300px; overflow-y: auto;" id="job_desc_html_linkedin">{{ linkedin_job_description|safe }}</div>
                                <button class="btn btn-outline-primary d-flex align-items-center" onclick="copyJobDescriptionToClipboard('job_desc_html_linkedin', event)">
                                    <i class="bi bi-clipboard me-1"></i>
                                    {% trans "Copy" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">5</span>
                        <h6 class="mb-0 mx-1">{% trans "Configure Application Management" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <ol class="mb-3">
                            <li>{% trans "Click 'Continue' button" %}</li>
                            <li>{% trans "Find 'Manage applicants' option and click the pencil icon to edit" %}</li>
                            <li>{% trans "Change 'Manage Applications' from 'On LinkedIn' to 'On an External Website'" %}</li>
                            <li>{% trans "Copy and paste the application URL below:" %}</li>
                        </ol>

                        <div class="copy-section">
                            <div class="input-group">
                                <input type="text" class="form-control bg-light" value="https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=linkedin" readonly>
                                <button class="btn btn-outline-primary d-flex align-items-center" onclick="copyToClipboard('https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=linkedin', event)">
                                    <i class="bi bi-clipboard me-1"></i>
                                    {% trans "Copy" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 6 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">6</span>
                        <h6 class="mb-0 mx-1">{% trans "Review Qualifications" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <p class="mb-2">{% trans "Review and customize the ideal qualifications section:" %}</p>
                        <div class="alert alert-light border">
                            <small class="text-muted">{% trans "Reference - Skills from your job:" %}</small>
                            <div class="mt-2">
                                {% for skill in vacancy.skills %}
                                <p class="mb-1">• {{ skill }}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 7 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-primary rounded-circle text-white me-3">7</span>
                        <h6 class="mb-0 mx-1">{% trans "Finalize and Publish" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <ol class="mb-0">
                            <li>{% trans "Confirm your identity using your work email if required" %}</li>
                            <li>{% trans "Choose between free or promoted posting (Recommended: Free)" %}</li>
                            <li>{% trans "Click 'Post Job' button" %}</li>
                            <li class="text-success fw-bold">{% trans "Your job is now live on LinkedIn!" %} 🎉</li>
                        </ol>
                    </div>
                </div>

                <div class="alert alert-success mt-4">
                    <i class="bi bi-check-circle me-2"></i>
                    {% trans "Need help? Contact our support team if you encounter any issues during the posting process." %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Post on Any Portal Modal -->
<div class="modal fade" id="postAnyPortalModal" tabindex="-1" aria-labelledby="postAnyPortalModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white ">
                <h5 class="modal-title d-flex align-items-center" id="postAnyPortalModalLabel">
                    {% trans "Post this job on Other Portals" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="alert alert-info mb-4">
                    <i class="bi bi-info-circle me-2"></i>
                    {% trans "Use the provided details about this job to post it on any platform." %}
                </div>


                <!-- Step 2 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-success rounded-circle text-white me-3"> * </span>
                        <h6 class="mb-0 mx-1">{% trans "Copy Job Title" %}</h6>
                    </div>

                    <div class="copy-section ms-5">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light" value="{{ vacancy.vacancy_title }}" readonly>
                            <button class="btn btn-outline-success d-flex align-items-center" onclick="copyToClipboard('{{ vacancy.vacancy_title }}', event)">
                                <i class="bi bi-clipboard me-1"></i>
                                {% trans "Copy" %}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-success rounded-circle text-white me-3"> * </span>
                        <h6 class="mb-0 mx-1">{% trans "Configure Job Settings" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <div class="alert alert-light border">
                            <small class="text-muted">{% trans "Reference - Your job settings:" %}</small>
                            <div class="mt-2">
                                <p class="mb-1"><strong>{% trans "Location:" %}</strong> {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}</p>
                                <p class="mb-1"><strong>{% trans "Work Schedule:" %}</strong> {{ vacancy.work_schedule }}</p>
                                <p class="mb-0"><strong>{% trans "Office Schedule:" %}</strong> {{ vacancy.office_schedule }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-success rounded-circle text-white me-3"> * </span>
                        <h6 class="mb-0 mx-1">{% trans "Copy Job Description" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <small>{% trans "Note: Delete any placeholder text in the editor before pasting. You may have to re-apply some of the styling." %}</small>
                        </div>

                        <div class="copy-section">
                            <div class="input-group">
                                <div class="form-control bg-light job-description-copy-area" style="height: 300px; overflow-y: auto;" id="job_desc_html_other">{{ job_description|safe }}</div>
                                <button class="btn btn-outline-success d-flex align-items-center" onclick="copyJobDescriptionToClipboard('job_desc_html_other', event)">
                                    <i class="bi bi-clipboard me-1"></i>
                                    {% trans "Copy" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-success rounded-circle text-white me-3"> * </span>
                        <h6 class="mb-0 mx-1">{% trans "Direct the Applicants" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <ol class="mb-3">
                            <li>{% trans "Copy and paste the application URL below:" %}</li>
                        </ol>

                        <div class="copy-section">
                            <div class="input-group">
                                <input type="text" class="form-control bg-light" value="https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=other_portals" readonly>
                                <button class="btn btn-outline-success d-flex align-items-center" onclick="copyToClipboard('https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=other_portals', event)">
                                    <i class="bi bi-clipboard me-1"></i>
                                    {% trans "Copy" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 6 -->
                <div class="step-container mb-4">
                    <div class="step-header d-flex align-items-center mb-3">
                        <span class="step-number badge bg-success rounded-circle text-white me-3"> * </span>
                        <h6 class="mb-0 mx-1">{% trans "Review Qualifications" %}</h6>
                    </div>
                    <div class="step-description ms-5">
                        <div class="alert alert-light border">
                            <small class="text-muted">{% trans "Reference - Skills from your job:" %}</small>
                            <div class="mt-2">
                                {% for skill in vacancy.skills %}
                                <p class="mb-1">• {{ skill }}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>


                <div class="alert alert-success mt-4">
                    <i class="bi bi-check-circle me-2"></i>
                    {% trans "Need help? Contact our support team if you encounter any issues during the posting process." %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Description Modal -->
<div class="modal fade" id="jobDescriptionModal" tabindex="-1" aria-labelledby="jobDescriptionModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="jobDescriptionModalLabel">
          <i class="bi bi-file-text me-2"></i>
          {% trans "Job Description" %} - {{ vacancy.vacancy_title }}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="job-description-display" id="job-description-area">
          {{ job_description|safe }}
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
      </div>
    </div>
  </div>
</div>

<!-- Change Status Modal -->
<div class="modal fade" id="changeStatusModal" tabindex="-1" aria-labelledby="changeStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeStatusModalLabel">{% trans "Change Vacancy Status" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-gray-700">
                    {% trans "Current Status:" %}
                    <span class="badge {% if vacancy.vacancy_status == 'Active' %}bg-success{% elif vacancy.vacancy_status == 'On-Hold' %}bg-warning{% else %}bg-danger{% endif %}">
                        {{ vacancy.vacancy_status }}
                    </span>
                </p>
                <p class="text-gray-700">
                    <strong>{% trans "Note:" %}</strong> {% trans "Changing the status will affect the visibility of the vacancy." %}
                </p>
                <ul class="list-disc pl-5 text-gray-700">
                    <li><strong>Archived:</strong> {% trans "The vacancy will no longer exist on boards and be closed but be accesible internally." %}</li>
                    <li><strong>On-Hold:</strong> {% trans "The vacancy will stop accepting new applications until changed." %}</li>
                    <li><strong>Active:</strong> {% trans "The vacancy will be re-opened for new applications." %}</li>
                    <li><strong>Deleted:</strong> {% trans "The vacancy will be permanently deleted. This action cannot be undone." %}</li>
                </ul>
                <form id="changeStatusForm" method="POST" onsubmit="handleStatusChange(event)">
                    {% csrf_token %}
                    <div class="mb-3">
                        <!-- Status Selection -->
                        <label for="vacancyStatus" class="form-label font-bold text-lg text-gray-800">{% trans "Select New Status" %}</label>
                        <select
                            class="form-select border-2 border-blue-500 rounded-lg p-3 text-gray-700 text-lg font-medium focus:ring-2 focus:ring-blue-300 focus:outline-none"
                            id="new_vacancyStatus"
                            name="new_vacancyStatus"
                            required
                        >
                            <option value="Active" {% if vacancy.vacancy_status == 'Active' %}selected{% endif %}>
                                Active
                            </option>
                            <option value="On-Hold" {% if vacancy.vacancy_status == 'On-Hold' %}selected{% endif %}>
                                On-Hold
                            </option>
                            <option value="Archived" {% if vacancy.vacancy_status == 'Archived' %}selected{% endif %}>
                                Archived
                            </option>
                            <option value="Deleted" {% if vacancy.vacancy_status == 'Deleted' %}selected{% endif %}>
                                Deleted
                            </option>
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" class="btn btn-primary">{% trans "Confirm Status" %}</button>
                    </div>
                    <input type="hidden" name="vacancy_id" value="{{ vacancy.vacancy_id }}" />
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
      // Function to calculate color based on score (same as before)
      function getColorForScore(score) {
        // Normalize score to 0-1 range
        const normalized = score / 100;

        // Define color stops
        const red = { r: 239, g: 68, b: 68 };    // #ef4444
        const yellow = { r: 234, g: 179, b: 8 };  // #eab308
        const green = { r: 34, g: 197, b: 94 };   // #22c55e

        let r, g, b;

        if (normalized <= 0.5) {
          // Transition from red to yellow
          const factor = normalized * 2;
          r = red.r + (yellow.r - red.r) * factor;
          g = red.g + (yellow.g - red.g) * factor;
          b = red.b + (yellow.b - red.b) * factor;
        } else {
          // Transition from yellow to green
          const factor = (normalized - 0.5) * 2;
          r = yellow.r + (green.r - yellow.r) * factor;
          g = yellow.g + (green.g - yellow.g) * factor;
          b = yellow.b + (green.b - yellow.b) * factor;
        }

        return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
      }

      // Apply color to all applicant score bars
      const applicantScoreBars = document.querySelectorAll('.applicant-score-bar');
      applicantScoreBars.forEach(bar => {
        const score = parseInt(bar.getAttribute('data-score'));
        if (!isNaN(score)) {
          bar.style.backgroundColor = getColorForScore(score);
        }
      });
    });
    function handleStatusChange(event) {
        event.preventDefault();
        const form = event.target;
        const newStatus = document.getElementById('new_vacancyStatus').value;
        const vacancyId = "{{ vacancy.vacancy_id }}";

        updateVacancyStatus(vacancyId, newStatus);
    }

    function copyToClipboard(text, event) {
        navigator.clipboard.writeText(text)
            .then(() => {
                console.log('Copied to clipboard:', text);
                // Make the button that this onclick function was used on green
                event.target.classList.add('btn-success');
                // Button text should be replaced by a tick
                event.target.innerHTML = '<i class="bi bi-check"></i>';
            })
            .catch(err => {
                console.error('Failed to copy: ', err);
            });
    }

    // Enhanced copy function for job descriptions that preserves formatting
    function copyJobDescriptionToClipboard(elementId, event) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error('Element not found:', elementId);
            return;
        }

        // Get the HTML content
        const htmlContent = element.innerHTML;

        // Create a temporary div to convert HTML to formatted text
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        // Convert HTML to formatted plain text
        let formattedText = '';

        function processNode(node) {
            if (node.nodeType === Node.TEXT_NODE) {
                formattedText += node.textContent;
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                const tagName = node.tagName.toLowerCase();

                // Add appropriate formatting based on HTML tags
                switch (tagName) {
                    case 'h1':
                    case 'h2':
                    case 'h3':
                    case 'h4':
                    case 'h5':
                    case 'h6':
                        formattedText += '\n\n';
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        formattedText += '\n\n';
                        break;
                    case 'p':
                        formattedText += '\n\n';
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        formattedText += '\n';
                        break;
                    case 'br':
                        formattedText += '\n';
                        break;
                    case 'li':
                        formattedText += '\n• ';
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        break;
                    case 'ul':
                    case 'ol':
                        formattedText += '\n';
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        formattedText += '\n';
                        break;
                    case 'strong':
                    case 'b':
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        break;
                    case 'em':
                    case 'i':
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        break;
                    case 'hr':
                        formattedText += '\n\n---\n\n';
                        break;
                    default:
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        break;
                }
            }
        }

        processNode(tempDiv);

        // Clean up extra whitespace while preserving intentional formatting
        formattedText = formattedText
            .replace(/\n{3,}/g, '\n\n') // Replace multiple newlines with double newlines
            .replace(/^\n+/, '') // Remove leading newlines
            .replace(/\n+$/, '\n'); // Replace trailing newlines with single newline

        // Copy to clipboard
        navigator.clipboard.writeText(formattedText)
            .then(() => {
                console.log('Job description copied to clipboard with formatting preserved');
                // Update button appearance
                const button = event.target.closest('button');
                if (button) {
                    button.classList.add('btn-success');
                    button.innerHTML = '<i class="bi bi-check me-1"></i>Copied!';

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.classList.remove('btn-success');
                        button.innerHTML = '<i class="bi bi-clipboard me-1"></i>Copy';
                    }, 2000);
                }
            })
            .catch(err => {
                console.error('Failed to copy job description: ', err);
                // Fallback to simple text copy
                navigator.clipboard.writeText(element.textContent || element.innerText)
                    .then(() => {
                        console.log('Fallback: copied plain text to clipboard');
                    })
                    .catch(fallbackErr => {
                        console.error('Fallback copy also failed: ', fallbackErr);
                    });
            });
    }

</script>

<script>
  // Chart Data Configuration
  const applicantsOverTime = {
    labels: [{% for week in applicants_over_time %}"{{ week.week|date:'M d' }}",{% endfor %}],
    datasets: [{
      label: "Applicants",
      data: [{% for week in applicants_over_time %}{{ week.count }},{% endfor %}],
      borderColor: "rgb(59, 130, 246)",
      backgroundColor: "rgba(59, 130, 246, 0.1)",
      tension: 0.4,
      fill: true,
    }]
  };

  const jobPortalData = {
    labels: [{% for source in applications_by_source %}"{{ source.application_source }}",{% endfor %}],
    datasets: [{
      label: "Applicants",
      data: [{% for source in applications_by_source %}"{{ source.count }}",{% endfor %}],
      backgroundColor: [
        "rgba(59, 130, 246, 0.8)",
        "rgba(16, 185, 129, 0.8)",
        "rgba(245, 158, 11, 0.8)",
        "rgba(245, 15, 110, 0.8)",
        "rgba(134, 15, 110, 0.8)",
        "rgba(215, 221, 37, 0.8)",
        "rgba(84, 242, 221, 0.8)",
      ],
      borderWidth: 0,
    }]
  };

  const statusData = {
    labels: [{% for source in applications_by_state %}"{{ source.application_state }}",{% endfor %}],
    datasets: [{
      data: [{% for source in applications_by_state %}"{{ source.count }}",{% endfor %}],
      backgroundColor: [
        "rgba(239, 68, 68, 0.8)",
        "rgba(59, 130, 246, 0.8)",
        "rgba(16, 185, 129, 0.8)",
        "rgba(245, 158, 11, 0.8)",
        "rgba(139, 92, 246, 0.8)",
      ],
      borderWidth: 0,
    }]
  };

  // Chart Initializations
  const lineCtx = document.getElementById("applicantsLineChart").getContext("2d");
  new Chart(lineCtx, {
    type: "line",
    data: applicantsOverTime,
    options: {
      responsive: true,
      plugins: { legend: { display: false } },
      scales: {
        y: {
          beginAtZero: true,
          ticks: { precision: 0 }
        }
      }
    }
  });

  const barCtx = document.getElementById("jobPortalBarChart").getContext("2d");
  new Chart(barCtx, {
    type: "bar",
    data: jobPortalData,
    options: {
      responsive: true,
      plugins: { legend: { display: false } },
      scales: {
        y: {
          beginAtZero: true,
          ticks: { precision: 0 }
        }
      }
    }
  });

  const pieCtx = document.getElementById("statusPieChart").getContext("2d");
  new Chart(pieCtx, {
    type: "pie",
    data: statusData,
    options: {
      responsive: true,
      maintainAspectRatio: true,
      plugins: {
        legend: {
          position: "right",
          labels: {
            boxWidth: 15,
            font: { size: 13 }
          }
        }
      }
    }
  });


  function updateVacancyStatus(vacancyId, status) {
    fetch(`/update_vacancy_status/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': '{{ csrf_token }}'
      },
      body: JSON.stringify({ vacancy_id: vacancyId, status: status })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('Failed to update vacancy status.');
      }
    });
  }

  // Toast notification functions
  function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    const toastMessage = document.querySelector('.toast-message');

    // Set the message
    toastMessage.textContent = message;

    // Reset classes first
    toast.className = 'toast';

    // Add appropriate classes
    if (type === 'error') {
      toast.classList.add('error', 'show');
    } else {
      toast.classList.add('show');
    }

    // Auto hide after 5 seconds
    setTimeout(() => {
      toast.className = 'toast';
    }, 5000);
  }

  // Handle bulk email form submission
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize toast close button
    const toastClose = document.querySelector('.toast-close');
    if (toastClose) {
      toastClose.addEventListener('click', function() {
        document.getElementById('toast').className = 'toast';
      });
    }

  // Live preview for bulk email modal
  (function() {
    function escapeHtml(unsafe) {
      return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/\"/g, "&quot;")
        .replace(/'/g, "&#039;");
    }

    function updatePreview() {
      var subjectEl = document.getElementById('emailSubject');
      var bodyEl = document.getElementById('emailBody');
      var previewSubject = document.getElementById('previewSubject');
      var previewEmailBody = document.getElementById('previewEmailBody');
      if (!subjectEl || !bodyEl || !previewSubject || !previewEmailBody) return;

      // Subject
      previewSubject.textContent = subjectEl.value || '';

      // Body: mimic Django |linebreaks (paragraphs and br)
      var text = bodyEl.value || '';
      var paragraphs = text.split(/\n{2,}/); // split on blank lines
      var html = paragraphs.map(function(p) {
        return '<p>' + escapeHtml(p).replace(/\n/g, '<br>') + '</p>';
      }).join('');
      previewEmailBody.innerHTML = html || '<p></p>';
    }

    // Bind listeners now (we are already in DOMContentLoaded above)
    var subjectEl = document.getElementById('emailSubject');
    var bodyEl = document.getElementById('emailBody');
    if (subjectEl) subjectEl.addEventListener('input', updatePreview);
    if (bodyEl) bodyEl.addEventListener('input', updatePreview);

    // Initialize on modal show
    var commModal = document.getElementById('communicationModal');
    if (commModal && window.bootstrap) {
      commModal.addEventListener('shown.bs.modal', updatePreview);
    }
  })();


    // Handle bulk email form submission
    const bulkMailForm = document.getElementById('bulkMailForm');
    if (bulkMailForm) {
      bulkMailForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = document.getElementById('sendBulkEmailsBtn');

        // Disable button and show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';

        // Send the form data via fetch
        fetch('{% url "send_bulk_mails" %}', {
          method: 'POST',
          body: formData,
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          // Re-enable button
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Send Emails';

          if (data.success) {
            // Show success message
            showToast(data.message || 'Emails are being sent in the background.');

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('communicationModal'));
            modal.hide();

            // Reset the form
            bulkMailForm.reset();
          } else {
            // Show error message
            showToast(data.message || 'Failed to send emails. Please try again.', 'error');
          }
        })
        .catch(error => {
          // Re-enable button
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Send Emails';

          // Show error message
          showToast('An error occurred. Please try again.', 'error');
          console.error('Error:', error);
        });
      });
    }
  });
</script>

{% endblock %}

<style>
/* LinkedIn Modal Styling */
#postLinkedInModal .step-container {
    border-left: 3px solid #0077b5;
    padding-left: 1rem;
    margin-left: 1rem;
}

#postLinkedInModal .step-number {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

#postLinkedInModal .copy-section {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

#postLinkedInModal .copy-section .form-control {
    border: 1px solid #ced4da;
}

#postLinkedInModal .step-description ol {
    padding-left: 1.2rem;
}

#postLinkedInModal .step-description ol li {
    margin-bottom: 0.5rem;
}

#postLinkedInModal .modal-header {
    background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
}

/* Job Description Display Styling */
.job-description-display {
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
    border-radius: 8px;
    max-height: 70vh;
    overflow-y: auto;
}

.job-description-display h1,
.job-description-display h2,
.job-description-display h3,
.job-description-display h4,
.job-description-display h5,
.job-description-display h6 {
    color: #2c3e50;
    margin-top: 24px;
    margin-bottom: 12px;
    font-weight: 600;
}

.job-description-display h1:first-child,
.job-description-display h2:first-child,
.job-description-display h3:first-child,
.job-description-display h4:first-child,
.job-description-display h5:first-child,
.job-description-display h6:first-child {
    margin-top: 0;
}

.job-description-display p {
    margin-bottom: 16px;
    line-height: 1.6;
}

.job-description-display ul,
.job-description-display ol {
    margin-bottom: 16px;
    padding-left: 24px;
}

.job-description-display li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.job-description-display strong,
.job-description-display b {
    font-weight: 600;
    color: #2c3e50;
}

.job-description-display em,
.job-description-display i {
    font-style: italic;
}

.job-description-display blockquote {
    border-left: 4px solid #e9ecef;
    padding-left: 16px;
    margin: 16px 0;
    color: #6c757d;
    font-style: italic;
}

.job-description-display hr {
    border: none;
    border-top: 1px solid #e9ecef;
    margin: 24px 0;
}

.job-description-display table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
}

.job-description-display th,
.job-description-display td {
    border: 1px solid #e9ecef;
    padding: 8px 12px;
    text-align: left;
}

.job-description-display th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Job Description Copy Area Styling */
.job-description-copy-area {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.6 !important;
    color: #333 !important;
    padding: 16px !important;
    border: 1px solid #ced4da !important;
}

.job-description-copy-area h1,
.job-description-copy-area h2,
.job-description-copy-area h3,
.job-description-copy-area h4,
.job-description-copy-area h5,
.job-description-copy-area h6 {
    color: #2c3e50 !important;
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    font-weight: 600 !important;
}

.job-description-copy-area h1:first-child,
.job-description-copy-area h2:first-child,
.job-description-copy-area h3:first-child,
.job-description-copy-area h4:first-child,
.job-description-copy-area h5:first-child,
.job-description-copy-area h6:first-child {
    margin-top: 0 !important;
}

.job-description-copy-area p {
    margin-bottom: 12px !important;
    line-height: 1.6 !important;
}

.job-description-copy-area ul,
.job-description-copy-area ol {
    margin-bottom: 12px !important;
    padding-left: 20px !important;
}

.job-description-copy-area li {
    margin-bottom: 6px !important;
    line-height: 1.5 !important;
}

.job-description-copy-area strong,
.job-description-copy-area b {
    font-weight: 600 !important;
    color: #2c3e50 !important;
}

.job-description-copy-area em,
.job-description-copy-area i {
    font-style: italic !important;
}

#postLinkedInModal .alert-info {
    border-left: 4px solid #0dcaf0;
    background-color: #f0f9ff;
}

#postLinkedInModal .alert-warning {
    border-left: 4px solid #ffc107;
    background-color: #fffbf0;
}

#postLinkedInModal .alert-success {
    border-left: 4px solid #198754;
    background-color: #f0fff4;
}
</style>